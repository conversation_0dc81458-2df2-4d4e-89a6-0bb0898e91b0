# kafka-topics

A repo for the creation and maintainence of kafka topics. 

The deloyment currently uses ansible targrated at boxes within the data (UTP) VPC.

Current hosts are one of the stage brokers and the prod kafka bastion.

However, the ansible module library doesn't need to be on the broker, but this is needed to get around network issues.

## Techology

Using an [ansible-kafka-admin module](https://github.com/StephenSorriaux/ansible-kafka-admin) to manage kafka topics which can create, delete and modfit topics basic on config.

Wanted to use an [terraform module](https://github.com/Mongey/terraform-provider-kafka) but network issues caused a problem. A proper solution would mean a lot of work (Which needs to do done) and didn't want to put in a messy hack.

### Requirements

[Ansible-kafka-admin module](https://github.com/StephenSorriaux/ansible-kafka-admin) needs to be installed and have a number of [python modules installed](requirements.txt) installed. The module warns to only use these version.

the ansible module can be installed via

```
ansible-galaxy install -r ansible/roles/requirements.yml --roles-path ansible/roles
```

There is also a [dockerfile](DockerFile) which can help.

## How to use

there are two main topic config locations one for [stage](ansible/inventory/stage/group_vars/all.yml) and the other for [prod](ansible/inventory/prod/group_vars/all.yml). Adding and adjusting the config will change the topics onces deployed.

You can run a dry run to show you what topics will be affect in a run.

```
make dry-run ENV=stage
```

to run it you can do 

```
make dry-run ENV=stage
```

But its best to do these changes via buildkite deployment

## Notes

* Currenly stage and prod kafka's are different. stage is running on self managed EC2 and prod is MSK.
* Buildkite is being running in a different account and a different VPC. we currently don;t have a peer connection set up because of issues. This means in order to manage the topics we go in via the bastions on prod and staignt onto the broker for stage. However, did we had a peer connection we won;t need to ssh into anything at all. 



steps:
  - label: "Stage deployment"
    key: "stage_deployment"
    command:
        - make run ENV=stage EXTRA_ARGUMENTS='--extra-vars "profile=data-sandbox"'
    plugins:
        docker-compose#v2.1.0:
            run: kafka-topic-builder
            config: .buildkite/docker-compose.buildkite.yml
    concurrency: 1
    concurrency_group: "kafka-topic-runner"
    agents:
        queue: 'pepper-v6'
    branches:
      - master

  - wait

  - label: "prod dry run"
    key: "prod_dry_run"
    command:
        - make dry-run ENV=prod EXTRA_ARGUMENTS='--extra-vars "profile=data"'
    plugins:
        docker-compose#v2.1.0:
            run: kafka-topic-builder
            config: .buildkite/docker-compose.buildkite.yml
    concurrency: 1
    concurrency_group: "kafka-topic-runner"
    agents:
        queue: 'pepper-v6'

  - wait

  - block: 'Release to Production'
    branches:
        - master

  - label: "prod deployment"
    key: "prod_deployment"
    command:
        - make run ENV=prod EXTRA_ARGUMENTS='--extra-vars "profile=data"'
    plugins:
        docker-compose#v2.1.0:
            run: kafka-topic-builder
            config: .buildkite/docker-compose.buildkite.yml
    concurrency: 1
    concurrency_group: "kafka-topic-runner"
    agents:
        queue: 'pepper-v6'
    branches:
      - master
---
    - name: "install packages"
      pip:
        name: "{{ item }}"
      become: yes
      loop: 
        - "kafka-python==1.4.4"
        - "kazoo==2.6.1"
        - "pure-sasl==0.5.1"

    - set_fact:
        profile_command: "--profile {{ profile }}"
      check_mode: false
      when: "profile is defined"

    - name: Find MSK cluster the AWS CLI
      local_action: command aws kafka list-clusters --region {{ region }} {{ profile_command | default("") }} --cluster-name-filter {{ cluster_name }}
      changed_when: false
      when: "bootstrap_servers is not defined"
      check_mode: false
      register: kafka_cluster_output

    - name: Find MSK cluster brokers the AWS CLI
      local_action: command aws kafka get-bootstrap-brokers --region {{region}} {{ profile_command | default("") }}  --cluster-arn {{ kafka_cluster_output.stdout | from_json | json_query('ClusterInfoList[0].ClusterArn')}}
      changed_when: false
      when: "bootstrap_servers is not defined"
      check_mode: false
      register: bootstrap_ouput

    - name: Parse reponse to collect the cluster ARN
      when: "zookeeper_connection is not defined"
      check_mode: false
      set_fact:
        zookeeper_connection: "{{ kafka_cluster_output.stdout | from_json | json_query('ClusterInfoList[0].ZookeeperConnectString')}}"
      
    - name: Parse reponse to collect the bootstrap_brokers
      when: "bootstrap_servers is not defined"
      check_mode: false
      set_fact:
        bootstrap_servers: "{{ bootstrap_ouput.stdout | from_json | json_query('BootstrapBrokerStringTls')}}"

    - name: "create topic"
      kafka_lib:
        resource: "topic"
        api_version: "2.1.0"
        name: "{{ item.name }}"
        partitions: "{{ item.partitions }}"
        replica_factor: "{{ item.replica_factor }}"
        options:
          cleanup.policy: "{{ item.cleanup_policy | default(omit) }}"
          retention.ms: "{{ item.retention_ms | default(omit) }}"
          min.insync.replicas: "{{ item.min_insync_replicas | default(omit) }}"
          retention.bytes: "{{ item.retention_bytes | default(omit) }}"
          delete.retention.ms: "{{ item.delete_retention_ms | default(omit) }}"
          segment.ms: "{{ item.segment_ms | default(omit) }}"
          segment.bytes: "{{ item.segment_bytes | default(omit) }}"
          min.cleanable.dirty.ratio: "{{ item.min_cleanable_dirty_ratio | default(omit) }}"
          
        security_protocol: "{{ security_protocol | default(omit) }}"
        state: "{{ item.state | default('present') }}"
        zookeeper: "{{ zookeeper_connection }}"
        bootstrap_servers: "{{ bootstrap_servers }}"
      loop: "{{ topics }}"

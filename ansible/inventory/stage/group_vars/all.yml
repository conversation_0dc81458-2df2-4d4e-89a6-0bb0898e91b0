cluster_name: eris
security_protocol: SSL
region: us-east-1
topics:
  - name: "hub-risk-management-asic"
    partitions: 3
    replica_factor: 2
    retention_ms: *********

  - name: "hub-risk-management-asic-uk"
    partitions: 3
    replica_factor: 2
    retention_ms: *********

  - name: "hub-risk-management-fca"
    partitions: 3
    replica_factor: 2
    retention_ms: *********

  ########################################################################################################################################################
  # CTrader Stream FCA
  ########################################################################################################################################################
  - name: "fca-ctrader-deal-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: *********

  - name: "fca-ctrader-order-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: ***********
    delete_retention_ms: *********
    cleanup_policy: "compact"

  - name: "fca-ctrader-trader-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: -1
    cleanup_policy: "compact"

  - name: "fca-ctrader-group-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: -1
    cleanup_policy: "compact"

  - name: "fca-ctrader-symbol-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: -1
    cleanup_policy: "compact"

  - name: "fca-ctrader-balance-history-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: *********
    cleanup_policy: "compact"

  - name: "fca-ctrader-position-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: ***********
    delete_retention_ms: *********
    cleanup_policy: "compact"

  - name: "fca-ctrader-symbol-json"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact"

  - name: "fca-ctrader-trader-table-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: -1
    cleanup_policy: "compact"

  - name: "fca-ctrader-user-account-json"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact"

  - name: "fca-ctrader-end-of-day-trader-report-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: *********

  ########################################################################################################################################################
  # CTrader Stream Sandbox
  ########################################################################################################################################################

  - name: "sandbox-ctrader-deal-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: *********

  - name: "sandbox-ctrader-order-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: *********
    delete_retention_ms: *********
    cleanup_policy: "compact"

  - name: "sandbox-ctrader-trader-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: -1
    cleanup_policy: "compact"

  - name: "sandbox-ctrader-group-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: -1
    cleanup_policy: "compact"

  - name: "sandbox-ctrader-symbol-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: -1
    cleanup_policy: "compact"

  - name: "sandbox-ctrader-balance-history-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: *********
    cleanup_policy: "compact"

  - name: "sandbox-ctrader-position-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: *********
    delete_retention_ms: *********

  - name: "sandbox-ctrader-symbol-json"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact"

  - name: "asic-ctrader-trader-table-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: -1
    cleanup_policy: "compact"

  - name: "sandbox-ctrader-user-account-json"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact"

  - name: "sandbox-ctrader-end-of-day-trader-report-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: *********

  ########################################################################################################################################################
  # CTrader Stream ASIC
  ########################################################################################################################################################

  - name: "asic-ctrader-deal-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: *********

  - name: "asic-ctrader-order-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: *********
    delete_retention_ms: *********
    cleanup_policy: "compact"

  - name: "asic-ctrader-trader-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: -1
    cleanup_policy: "compact"

  - name: "asic-ctrader-group-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: -1
    cleanup_policy: "compact"

  - name: "asic-ctrader-symbol-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: -1
    cleanup_policy: "compact"

  - name: "asic-ctrader-balance-history-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: *********
    cleanup_policy: "compact"

  - name: "asic-ctrader-position-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: *********
    delete_retention_ms: *********

  - name: "asic-ctrader-symbol-json"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact"

  - name: "asic-ctrader-trader-table-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: -1
    cleanup_policy: "compact"

  - name: "asic-ctrader-user-account-json"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact"

  - name: "asic-ctrader-end-of-day-trader-report-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: *********

  ########################################################################################################################################################
  # CTrader Stream
  ########################################################################################################################################################

  - name: "ctrader-deal-json"
    partitions: 3
    replica_factor: 2
    retention_ms: *********

  - name: "ctrader-stream-dlq"
    partitions: 3
    replica_factor: 2
    retention_ms: *********

  - name: "ctrader-builder-deal-dlq-json"
    partitions: 3
    replica_factor: 2
    retention_ms: *********

  ########################################################################################################################################################

  - name: "connect-configs"
    partitions: 1
    replica_factor: 3

  - name: "connect-offsets"
    partitions: 24
    replica_factor: 3
    cleanup_policy: "compact"

  - name: "connect-status"
    partitions: 10
    replica_factor: 3
    cleanup_policy: "compact"

  - name: "s3-sink-connect-configs"
    partitions: 1
    replica_factor: 3
    cleanup_policy: "compact"

  - name: "s3-sink-connect-offsets"
    partitions: 25
    replica_factor: 3
    cleanup_policy: "compact"

  - name: "s3-sink-connect-status"
    partitions: 5
    replica_factor: 3
    cleanup_policy: "compact"

  - name: "s3-sink_connect-configs"
    partitions: 1
    replica_factor: 3
    cleanup_policy: "compact"

  - name: "snappy-sink-connect-configs"
    partitions: 1
    replica_factor: 3
    cleanup_policy: "compact"

  - name: "snappy-sink-connect-offsets"
    partitions: 25
    replica_factor: 3
    cleanup_policy: "compact"

  - name: "snappy-sink-connect-status"
    partitions: 5
    replica_factor: 3
    cleanup_policy: "compact"

  - name: "trade-sink-connect-configs"
    partitions: 1
    replica_factor: 3
    cleanup_policy: "compact"

  - name: "trade-sink-connect-offsets"
    partitions: 25
    replica_factor: 3
    cleanup_policy: "compact"

  - name: "trade-sink2-connect-status"
    partitions: 5
    replica_factor: 3
    cleanup_policy: "compact"

  - name: "trade-sink2-connect-configs"
    partitions: 1
    replica_factor: 3
    cleanup_policy: "compact"

  - name: "trade-sink2-connect-offsets"
    partitions: 25
    replica_factor: 3
    cleanup_policy: "compact"

  - name: "trade-sink2-connect-status"
    partitions: 5
    replica_factor: 3
    cleanup_policy: "compact"

  - name: "utp-sink-connect-configs"
    partitions: 1
    replica_factor: 3
    cleanup_policy: "compact"

  - name: "utp-sink-connect-offsets"
    partitions: 25
    replica_factor: 3
    cleanup_policy: "compact"

  - name: "utp-sink-connect-status"
    partitions: 5
    replica_factor: 3
    cleanup_policy: "compact"

  - name: "mt4-user-account-raw"
    partitions: 9
    replica_factor: 3
    retention_ms: *********

  - name: "mt5-user-account-raw"
    partitions: 9
    replica_factor: 3
    retention_ms: *********

  - name: "trade-processor-dlq"
    partitions: 3
    replica_factor: 2

  - name: "user-account"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact"

  - name: "utp-stage-ctrader"
    partitions: 8
    replica_factor: 3
    retention_ms: *********

  - name: "utp-stage-mt4"
    partitions: 8
    replica_factor: 3
    retention_ms: *********

  - name: "utp-stage-demo-mt4"
    partitions: 8
    replica_factor: 3
    retention_ms: *********

  - name: "utp-stage-mt5"
    partitions: 8
    replica_factor: 3
    retention_ms: *********

  - name: "mt4-transactions-raw"
    partitions: 3
    replica_factor: 2
    retention_ms: *********

  - name: "utp-stage-utprecord.v2"
    partitions: 8
    replica_factor: 3
    retention_ms: *********
    delete_retention_ms: 100
    state: absent

  - name: "utp-stage-utprecord.v3"
    partitions: 9
    replica_factor: 3
    retention_ms: -1
    retention_bytes: -1
    min_insync_replicas: 2

  - name: "utp-stage-enriched"
    partitions: 8
    replica_factor: 3
    retention_ms: *********

  - name: "utpEnrichmentLoginTopic"
    partitions: 4
    replica_factor: 2

  - name: "utp-positions"
    partitions: 9
    replica_factor: 2
    retention_ms: *********
    cleanup_policy: "delete"

  - name: "utp-open-positions"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact"
    delete_retention_ms: 7200000
    segment_ms: 3600000
    segment_bytes: 15000000
    min_cleanable_dirty_ratio: 0.01

  - name: "utp-stage-demo-utprecord.v1"
    partitions: 9
    replica_factor: 2
    retention_ms: *********
    cleanup_policy: "delete"

  - name: "tick-stream-onezero"
    min_insync_replicas: 1
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact,delete"
    retention_ms: *********
    delete_retention_ms: *********

  - name: "risk-price-json"
    partitions: 3
    replica_factor: 2
    retention_ms: *********

  - name: "risk-price-aggregation-json"
    partitions: 3
    replica_factor: 2
    retention_ms: *********

  - name: "user-insert-raw"
    partitions: 3
    replica_factor: 2
    retention_ms: *********

  - name: "user-update-raw"
    partitions: 3
    replica_factor: 2
    retention_ms: *********

  - name: "secure-trader-account"
    partitions: 3
    replica_factor: 2
    retention_ms: *********

  - name: "rebate-process"
    partitions: 3
    replica_factor: 2
    retention_ms: *********

  - name: "trader-account"
    partitions: 3
    replica_factor: 2
    retention_ms: *********

  - name: "login-first-trade-json"
    partitions: 3
    replica_factor: 2
    retention_ms: -1
    cleanup_policy: "compact"

  - name: "login-last-trade-in-window-json"
    partitions: 3
    replica_factor: 2
    retention_ms: -1
    cleanup_policy: "compact"

  - name: "login-first-demo-trade-json"
    partitions: 3
    replica_factor: 2
    retention_ms: -1
    cleanup_policy: "compact"

  - name: "login-last-demo-trade-in-window-json"
    partitions: 3
    replica_factor: 2
    retention_ms: -1
    cleanup_policy: "compact"

  - name: "price-feed-uk"
    partitions: 1
    replica_factor: 2
    cleanup_policy: "compact"
    delete_retention_ms: ********* # 3 days for DG-1485
    min_insync_replicas: 1

  - name: "open-positions-sucden"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact"

  - name: "mt5-account-raw"
    partitions: 3
    replica_factor: 2
    retention_ms: *********


  ########################################################################################################################################################
  # Daily records
  ########################################################################################################################################################

  - name: "mt4-daily-raw"
    partitions: 3
    replica_factor: 2
    retention_ms: *********

  - name: "mt5-daily-raw"
    partitions: 3
    replica_factor: 2
    retention_ms: *********

  - name: "trader-daily"
    partitions: 3
    replica_factor: 2
    retention_ms: *********

  ########################################################################################################################################################
  # Monitoring pipeline
  ########################################################################################################################################################

  - name: "jaeger-spans"
    partitions: 3
    replica_factor: 2
    # one week, this is in order to try it on stage, look what goes in there and understand things better.
    # This number needs to go down when more application uses jaeger
    retention_ms: *********

  ########################################################################################################################################################
  # MIFID position value drop alert for fca customers
  ########################################################################################################################################################

  - name: "mifid-alert-stream"
    partitions: 3
    replica_factor: 2
    retention_ms: 86400000 # 24 hours

  - name: "mifid-alert-table"
    partitions: 3
    replica_factor: 2
    retention_ms: -1
    cleanup_policy: "compact"
    delete_retention_ms: 7200000
    segment_ms: 3600000
    min_cleanable_dirty_ratio: 0.01

  ########################################################################################################################################################
  # Transaction Stream
  # Topic containing trader transactions from Mt4, Mt5 and CTrader
  ########################################################################################################################################################

  - name: "unified-transactions"
    partitions: 3
    replica_factor: 2
    retention_ms: *********

  - name: "unified-transactions-dlq"
    partitions: 1
    replica_factor: 2
    retention_ms: *********

  - name: "backfil-mt4-transactions-raw"
    partitions: 3
    replica_factor: 2
    retention_ms: -1
    state: present

  - name: "backfil-fca-ctrader-balance-history-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: -1
    cleanup_policy: "compact"
    state: present

  - name: "backfil-asic-ctrader-balance-history-raw-protobuf"
    partitions: 3
    replica_factor: 2
    retention_ms: -1
    cleanup_policy: "compact"
    state: present

  ########################################################################################################################################################
  # Negative balance
  # Topics containing information relative to negative balance detection
  ########################################################################################################################################################

  - name: "negative-balance"
    partitions: 3
    replica_factor: 2
    retention_ms: ********* # 7 days

  - name: "stop-out-events"
    partitions: 3
    replica_factor: 2
    retention_ms: ********* # 7 days

  ########################################################################################################################################################
  # Compliance
  # Topics containing information relative to compliance
  ########################################################################################################################################################

  - name: "margin-call-events"
    partitions: 3
    replica_factor: 2
    retention_ms: ********* # 7 days

  - name: "tax-withholding"
    partitions: 3
    replica_factor: 2
    retention_ms: ********* # 7 days

  - name: "tax-withholding-dlq"
    partitions: 3
    replica_factor: 2
    retention_ms: 2764800000 # 2 month

  ########################################################################################################################################################
  # Excalibur
  # Topics containing information relative to Excalibur
  ########################################################################################################################################################

  - name: "risk-hub-aggeration-exposure"
    partitions: 3
    replica_factor: 2
    retention_ms: ********* # 7 days

  - name: "symbol_value"
    partitions: 3
    replica_factor: 2
    retention_ms: ********* # 7 days

  - name: "base-volume-exposure-event"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact"

  - name: "taker-symbol-price"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact"

  - name: "exchange-rate"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact"

  - name: "exposure-price"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact"

  ########################################################################################################################################################
  # Trader's account finaces
  # Topics containing information relative to traders acccounts finances
  ########################################################################################################################################################

  - name: "unified-trader-finance"
    partitions: 3
    replica_factor: 2
    retention_ms: ********* # 7 days
  
    ########################################################################################################################################################
    # OneZero Taker feed
    # Topics containing information relative to onezero taker price feed
    ########################################################################################################################################################

  - name: "ny-taker-order-book-onezero"
    min_insync_replicas: 1
    partitions: 9
    replica_factor: 2
    cleanup_policy: "compact,delete"
    retention_ms: *********
    delete_retention_ms: *********

  - name: "ld-taker-order-book-onezero"
    min_insync_replicas: 1
    partitions: 9
    replica_factor: 2
    cleanup_policy: "compact,delete"
    retention_ms: *********
    delete_retention_ms: *********

  ########################################################################################################################################################
  # Reconciler
  ########################################################################################################################################################

  - name: "reconciler-variation"
    partitions: 3
    replica_factor: 2
    retention_ms: ********* # 7 days
    cleanup_policy: "delete"

  - name: "paypal-transactions-asic-raw"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact,delete"

  - name: "paypal-transactions-fca-raw"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact,delete"

  - name: "paypal-transactions-scb-raw"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact,delete"

  - name: "paypal-transactions-cma-raw"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact,delete"

  - name: "paypal-transactions-cysec-raw"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact,delete"

  - name: "paypal-balances-asic-raw"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact,delete"

  - name: "paypal-balances-fca-raw"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact,delete"

  - name: "paypal-balances-scb-raw"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact,delete"

  - name: "paypal-balances-cma-raw"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact,delete"

  - name: "paypal-balances-cysec-raw"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact,delete"

  - name: "adyen-transactions-fca-raw"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact,delete"

  - name: "adyen-report-events-fca-raw"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact,delete"
  
  - name: "adyen-settlement-events-fca-raw"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact,delete"
  
  - name: "safecharge-transactions-all-raw"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact,delete"

  - name: "safecharge-settlement-events-all-raw"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact,delete"

  - name: "safecharge-movements-events-all-raw"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact,delete"

  - name: "worldpay-transactions-all-raw"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact,delete"

  - name: "worldpay-settlement-events-all-raw"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact,delete"

  - name: "worldpay-balances-events-all-raw"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact,delete"

  - name: "skrill-transactions-all-raw"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact,delete"

  - name: "skrill-balances-events-all-raw"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact,delete"

  - name: "neteller-transactions-all-raw"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact,delete"

  - name: "poli-transactions-all-raw"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact,delete"

  - name: "nab-transactions-all-raw"
    partitions: 3
    replica_factor: 2
    cleanup_policy: "compact,delete"

  - name: "reconciler-transaction-events-harmonised"
    partitions: 3
    replica_factor: 2
    retention_ms: ********* # 7 days
    cleanup_policy: "delete"
  
  - name: "reconciler-settlement-events-harmonised"
    partitions: 3
    replica_factor: 2
    retention_ms: ********* # 7 days
    cleanup_policy: "delete"

  - name: "reconciler-balances-events-harmonised"
    partitions: 3
    replica_factor: 2
    retention_ms: ********* # 7 days
    cleanup_policy: "delete"

########################################################################################################################################################
  # Trader Last Login
########################################################################################################################################################

  - name: "trader-last-login-json"
    partitions: 3
    replica_factor: 2
    retention_ms: *********
    cleanup_policy: "compact"